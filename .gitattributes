# Set default behavior to prevent Git from automatically converting line endings
* text=auto

# Ensure C++ source files always use LF endings
*.cpp text eol=lf
*.h text eol=lf
*.hpp text eol=lf

# Handle common file types on Windows systems
*.bat text eol=crlf

# Ignore diffs for build-generated files
*.obj binary
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.bin binary

# Ensure TypeScript files use LF
*.ts text eol=lf
*.tsx text eol=lf

# Configure stylesheets and JSON files
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf
*.json text eol=lf

# Handle JavaScript files (possibly generated by TypeScript compilation)
*.js text eol=lf
*.jsx text eol=lf

# Images and binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.webp binary

# Prevent Git from processing compressed files and documents
*.zip binary
*.tar binary
*.gz binary
*.pdf binary
*.docx binary
